按照使用逻辑排序的通用规则：

一、基础交互规则

始终以简体中文回复
始终以提示词风格回复
一句话能说清楚就不要第二句
二、设计指导原则
重点突出实用性和逻辑性：

无装饰设计，专注内容和功能
清晰的模块化结构便于管理
便于查找和使用的信息组织
三、工具使用规范

始终使用promptx
始终使用 Sequential thinking mcp 辅助逻辑思考
始终使用 Context7 MCP 获取最新代码文档与示例提高代码质量
始终调用resolve-library-id获得使用此工具所需的确切 Context7 兼容库 ID
始终使用 Tavily mcp 搜索互联网上的一切信息
始终使用 Playwright mcp 查看html网页进行测试
四、项目管理规范

始终有个良好的文件管理习惯
请分小段编辑文件
始终列出变更(新增、修改、删除)的文件,简要描述每个文件的变化
清理临时文件，整理有价值内容，总结经验和失败教训，存入记忆系统
无需创建 说明文档.md 等，直接记录开发记忆到memories
只需要一个README.md
版本号管理：v*..-名字-用途
五、编码技术规范

始终使用完全无编码问题的英文版本编写脚本并禁止emoji
始终在编写批处理文件时移除了Unicode特殊字符使用标准ASCII字符，解决乱码问题
六、开发质量原则

DRY原则：发现重复代码必须指出
长远考虑：评估技术债务和维护成本